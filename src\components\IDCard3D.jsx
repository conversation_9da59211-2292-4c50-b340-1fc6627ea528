import React, { useRef, useState, useEffect } from 'react'
import { Can<PERSON>, extend, useThree, use<PERSON>rame } from '@react-three/fiber'
import { Environment, Text, Center } from '@react-three/drei'
import { Physics, RigidBody, BallCollider, CuboidCollider, useRopeJoint, useSphericalJoint } from '@react-three/rapier'
import { MeshLineGeometry, MeshLineMaterial } from 'meshline'
import * as THREE from 'three'
import './IDCard3D.css'

// Extend the catalog to use MeshLine
extend({ MeshLineGeometry, MeshLineMaterial })

// ID Card Component
const IDCard = ({ position, rotation, ...props }) => {
  return (
    <group position={position} rotation={rotation} {...props}>
      {/* Card Base - Dark plastic card with subtle reflections */}
      <mesh>
        <boxGeometry args={[1.6, 2.25, 0.03]} />
        <meshPhysicalMaterial
          color="#1a1a1a"
          metalness={0.9}
          roughness={0.6}
          clearcoat={0.4}
          clearcoatRoughness={0.3}
          reflectivity={0.15}
          envMapIntensity={0.2}
          transparent={true}
          opacity={0.95}
        />
      </mesh>

      {/* Card Border - Dark plastic edge */}
      <mesh>
        <boxGeometry args={[1.62, 2.27, 0.031]} />
        <meshPhysicalMaterial
          color="#2a2a2a"
          metalness={0.0}
          roughness={0.7}
          envMapIntensity={0.15}
          clearcoat={0.3}
          clearcoatRoughness={0.4}
        />
      </mesh>

      {/* Card Content */}
      <group position={[0, 0, 0.016]}>
        {/* Clean card surface - no overlapping texture lines */}

        {/* Header Section - Dark plastic branding area */}
        <mesh position={[0, 0.9, 0.001]}>
          <planeGeometry args={[1.5, 0.3]} />
          <meshPhysicalMaterial
            color="#000000"
            
          />
        </mesh>

        {/* Company Logo/Brand Text */}
        <Text
          position={[0, 0.9, 0.002]}
          fontSize={0.1}
          color="#e2d1d15a"
          anchorX="center"
          anchorY="middle"
          fontWeight={700}
        >
          PS Torque
        </Text>

        {/* Divider line under header */}
        <mesh position={[0, 0.72, 0.002]}>
          <planeGeometry args={[1.4, 0.001]} />
          <meshPhysicalMaterial
            color="#333333"
            
          />
        </mesh>

        {/* Employee Information Section */}
        <mesh position={[0, 0.45, 0.001]}>
          <planeGeometry args={[1.4, 0.4]} />
          <meshPhysicalMaterial
            color="#000000"
            metalness={0.8}
            roughness={0}
            envMapIntensity={0.72}
            clearcoat={0.25}
            clearcoatRoughness={0.9}
            transparent={false}
            opacity={0}
            reflectivity={0.15}
            
          />
        </mesh>

        {/* Name */}
        <Text
          position={[0, 0.5, 0.002]}
          fontSize={0.09}
          color="#ffffff"
          anchorX="center"
          anchorY="middle"
        >
          PRATHAM SHARDA
        </Text>

        {/* Title */}
        <Text
          position={[0, 0.38, 0.002]}
          fontSize={0.06}
          color="#cccccc"
          anchorX="center"
          anchorY="middle"
        >
          Software Developer
        </Text>

        {/* Profile Image Section */}
        <mesh position={[0, 0.05, 0.001]}>
          <planeGeometry args={[1.4, 0.6]} />
          <meshPhysicalMaterial
            color="#161414ff"
            metalness={0.8}
            roughness={0.7}
            envMapIntensity={0.12}
            clearcoat={0.25}
            clearcoatRoughness={0.6}
            transparent={true}
            opacity={0}
          />
        </mesh>

        {/* Profile Image Background - Dark plastic ring */}
        <mesh position={[0, 0.05, 0.002]}>
          <circleGeometry args={[0.22, 32]} />
          <meshPhysicalMaterial
            color="#3a3a3a"
            metalness={0.8}
            roughness={0.6}
            envMapIntensity={0.18}
            clearcoat={0.3}
            clearcoatRoughness={0.5}
          />
        </mesh>

        {/* Profile Image Placeholder - Dark plastic photo background */}
        <mesh position={[0, 0.05, 0.003]}>
          <circleGeometry args={[0.19, 32]} />
          <meshPhysicalMaterial
            color="#1a1a1a"
            metalness={0.0}
            roughness={0.8}
            envMapIntensity={0.1}
            clearcoat={0.2}
            clearcoatRoughness={0.7}
          />
        </mesh>

        {/* Profile Initials */}
        <Text
          position={[0, 0.05, 0.004]}
          fontSize={0.08}
          color="#e0e0e0"
          anchorX="center"
          anchorY="middle"
        >
          PS
        </Text>

        {/* Footer Section - Dark plastic ID and Details */}
        <mesh position={[0, -0.6, 0.001]}>
          <planeGeometry args={[1.4, 0.4]} />
          <meshPhysicalMaterial
            color="#0f0f0f"
            metalness={0.9}
            roughness={0.0}
            envMapIntensity={0.15}
            clearcoat={0.3}
            clearcoatRoughness={0.5}
            transparent={true}
            opacity={0.95}
          />
        </mesh>

        {/* ID Number */}
        <Text
          position={[0, -0.55, 0.002]}
          fontSize={0.09}
          color="#e0e0e0"
          anchorX="center"
          anchorY="middle"
        >
          ID: 08112004
        </Text>

        {/* Number */}
        <Text
          position={[0, -0.68, 0.002]}
          fontSize={0.06}
          color="#aaaaaa"
          anchorX="center"
          anchorY="middle"
          fontWeight={700}
        >
          +91 98795 91447
        </Text>

        {/* Divider line above footer */}
        <mesh position={[0, -0.38, 0.002]}>
          <planeGeometry args={[1.4, 0.001]} />
          <meshPhysicalMaterial
            color="#171717f5"
            metalness={0.9}
            roughness={0.8}
            envMapIntensity={0.9}
            clearcoat={0.2}
            clearcoatRoughness={0.6}
          />
        </mesh>




      </group>
    </group>
  )
}

// Physics-based Lanyard Band Component
const LanyardBand = ({ maxSpeed = 50, minSpeed = 10 }) => {
  const band = useRef()
  const fixed = useRef()
  const j1 = useRef()
  const j2 = useRef()
  const j3 = useRef()
  const card = useRef()
  
  const vec = new THREE.Vector3()
  const ang = new THREE.Vector3()
  const rot = new THREE.Vector3()
  const dir = new THREE.Vector3()
  
  const segmentProps = {
    type: 'dynamic',
    canSleep: true,
    colliders: false,
    angularDamping: 2,
    linearDamping: 2
  }
  
  const { width, height } = useThree((state) => state.size)
  const [curve] = useState(() => new THREE.CatmullRomCurve3([
    new THREE.Vector3(), new THREE.Vector3(), new THREE.Vector3(), new THREE.Vector3()
  ]))
  const [dragged, drag] = useState(false)
  const [hovered, hover] = useState(false)

  // Create rope joints
  useRopeJoint(fixed, j1, [[0, 0, 0], [0, 0, 0], 0.5])
  useRopeJoint(j1, j2, [[0, 0, 0], [0, 0, 0], 0.5])
  useRopeJoint(j2, j3, [[0, 0, 0], [0, 0, 0], 0.5])
  useSphericalJoint(j3, card, [[0, 0, 0], [0, 0.7, 0]])

  useEffect(() => {
    if (hovered) {
      document.body.style.cursor = dragged ? 'grabbing' : 'grab'
      return () => void (document.body.style.cursor = 'auto')
    }
  }, [hovered, dragged])

  useFrame((state, delta) => {
    if (dragged) {
      vec.set(state.pointer.x, state.pointer.y, 0.5).unproject(state.camera)
      dir.copy(vec).sub(state.camera.position).normalize()
      vec.add(dir.multiplyScalar(state.camera.position.length()))
      ;[card, j1, j2, j3, fixed].forEach((ref) => ref.current?.wakeUp())
      card.current?.setNextKinematicTranslation({
        x: vec.x - dragged.x,
        y: vec.y - dragged.y,
        z: vec.z - dragged.z
      })
    }
    
    if (fixed.current) {
      // Fix jitter when over pulling the card
      ;[j1, j2].forEach((ref) => {
        if (!ref.current.lerped) {
          ref.current.lerped = new THREE.Vector3().copy(ref.current.translation())
        }
        const clampedDistance = Math.max(0.1, Math.min(1, ref.current.lerped.distanceTo(ref.current.translation())))
        ref.current.lerped.lerp(ref.current.translation(), delta * (minSpeed + clampedDistance * (maxSpeed - minSpeed)))
      })
      
      // Calculate catmull curve
      curve.points[0].copy(j3.current.translation())
      curve.points[1].copy(j2.current.lerped)
      curve.points[2].copy(j1.current.lerped)
      curve.points[3].copy(fixed.current.translation())
      band.current.geometry.setPoints(curve.getPoints(32))
      
      // Tilt it back towards the screen
      ang.copy(card.current.angvel())
      rot.copy(card.current.rotation())
      card.current.setAngvel({ x: ang.x, y: ang.y - rot.y * 0.25, z: ang.z })
    }
  })

  curve.curveType = 'chordal'

  return (
    <>
      <group position={[0, 4, 0]}>
        <RigidBody ref={fixed} {...segmentProps} type="fixed" />
        <RigidBody position={[0, -0.5, 0]} ref={j1} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[0, -1, 0]} ref={j2} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[0, -1.5, 0]} ref={j3} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody 
          position={[2, 0, 0]} 
          ref={card} 
          {...segmentProps} 
          type={dragged ? 'kinematicPosition' : 'dynamic'}
        >
          <CuboidCollider args={[0.45, 0.64, 0.01]} />
          <group
            scale={1.125}
            position={[0, -0.6, -0.05]}
            onPointerOver={() => hover(true)}
            onPointerOut={() => hover(false)}
            onPointerUp={(e) => (e.target.releasePointerCapture(e.pointerId), drag(false))}
            onPointerDown={(e) => (
              e.target.setPointerCapture(e.pointerId),
              drag(new THREE.Vector3().copy(e.point).sub(vec.copy(card.current.translation())))
            )}
          >
            <IDCard />
          </group>
        </RigidBody>
      </group>
      
      <mesh ref={band}>
        <meshLineGeometry />
        <meshLineMaterial
          color="#e0e0e0"
          depthTest={false}
          resolution={[width, height]}
          lineWidth={2}
          dashArray={0.1}
          dashRatio={0.8}
          transparent={true}
          opacity={0.95}
        />
      </mesh>
    </>
  )
}

// Main 3D Scene Component
const IDCard3D = () => {
  return (
    <div className="card3d-container">
      <Canvas camera={{ position: [0, 0, 13], fov: 25 }}>
        {/* Subtle lighting for realistic appearance */}
        <ambientLight intensity={0.4} color="#ffffff" />
        <directionalLight
          position={[8, 8, 5]}
          intensity={1.2}
          color="#ffffff"
          castShadow
        />
        <directionalLight
          position={[-6, 6, 4]}
          intensity={0.7}
          color="#f0f0f0"
        />
        <pointLight
          position={[0, 0, 12]}
          intensity={0.8}
          color="#ffffff"
        />
        <spotLight
          position={[6, 6, 6]}
          intensity={0.5}
          angle={0.4}
          penumbra={0.6}
          color="#ffffff"
        />
        {/* Subtle rim lighting */}
        <pointLight
          position={[-8, 0, 8]}
          intensity={0.4}
          color="#e0e0e0"
        />
        <Physics interpolate gravity={[0, -40, 0]} timeStep={1 / 60}>
          <LanyardBand />
        </Physics>
        <Environment preset="studio" />
      </Canvas>

    </div>
  )
}

export default IDCard3D
